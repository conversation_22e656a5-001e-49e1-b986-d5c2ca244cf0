-- ===================================================================
-- Database Performance Optimization Migration
-- ===================================================================

-- Create indexes for better query performance
-- ===================================================================

-- Patient table indexes
CREATE INDEX IF NOT EXISTS idx_patients_facility_id ON patients(facility_id) WHERE soft_deleted = false;
CREATE INDEX IF NOT EXISTS idx_patients_registration_date ON patients(registration_date) WHERE soft_deleted = false;
CREATE INDEX IF NOT EXISTS idx_patients_date_of_birth ON patients(date_of_birth) WHERE soft_deleted = false;
CREATE INDEX IF NOT EXISTS idx_patients_gender ON patients(gender) WHERE soft_deleted = false;
CREATE INDEX IF NOT EXISTS idx_patients_identifier ON patients(identifier_type, identifier_number) WHERE soft_deleted = false;
CREATE INDEX IF NOT EXISTS idx_patients_name ON patients(first_name, last_name) WHERE soft_deleted = false;
CREATE INDEX IF NOT EXISTS idx_patients_soft_deleted ON patients(soft_deleted);

-- Composite index for common search patterns
CREATE INDEX IF NOT EXISTS idx_patients_search_composite ON patients(facility_id, soft_deleted, registration_date DESC);

-- Patient contacts indexes
CREATE INDEX IF NOT EXISTS idx_patient_contacts_patient_id ON patient_contacts(patient_id);
CREATE INDEX IF NOT EXISTS idx_patient_contacts_mobile ON patient_contacts(mobile_number) WHERE mobile_number IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_patient_contacts_email ON patient_contacts(email) WHERE email IS NOT NULL;

-- Patient addresses indexes
CREATE INDEX IF NOT EXISTS idx_patient_addresses_patient_id ON patient_addresses(patient_id);
CREATE INDEX IF NOT EXISTS idx_patient_addresses_city ON patient_addresses(city_or_village);
CREATE INDEX IF NOT EXISTS idx_patient_addresses_state ON patient_addresses(state_id);
CREATE INDEX IF NOT EXISTS idx_patient_addresses_postal_code ON patient_addresses(pincode);

-- Emergency contacts indexes
CREATE INDEX IF NOT EXISTS idx_emergency_contacts_patient_id ON emergency_contacts(patient_id);
CREATE INDEX IF NOT EXISTS idx_emergency_contacts_phone ON emergency_contacts(phone_number);

-- Patient ABHA indexes
CREATE INDEX IF NOT EXISTS idx_patient_abha_patient_id ON patient_abha(patient_id);
CREATE INDEX IF NOT EXISTS idx_patient_abha_number ON patient_abha(abha_number) WHERE abha_number IS NOT NULL;

-- Patient insurance indexes
CREATE INDEX IF NOT EXISTS idx_patient_insurance_patient_id ON patient_insurance(patient_id);
CREATE INDEX IF NOT EXISTS idx_patient_insurance_policy ON patient_insurance(policy_number) WHERE policy_number IS NOT NULL;

-- Billing referral indexes
CREATE INDEX IF NOT EXISTS idx_billing_referral_patient_id ON billing_referral(patient_id);

-- Information sharing indexes
CREATE INDEX IF NOT EXISTS idx_information_sharing_patient_id ON information_sharing(patient_id);

-- Referrals indexes
CREATE INDEX IF NOT EXISTS idx_referrals_patient_id ON referrals(patient_id);
CREATE INDEX IF NOT EXISTS idx_referrals_to_facility ON referrals(to_facility_id);
CREATE INDEX IF NOT EXISTS idx_referrals_date ON referrals(referral_date);

-- Patient relationships indexes
CREATE INDEX IF NOT EXISTS idx_patient_relationships_patient_id ON patient_relationships(patient_id);
CREATE INDEX IF NOT EXISTS idx_patient_relationships_related_id ON patient_relationships(relative_id);

-- Lookup values indexes (only if lookup_values table exists)
-- CREATE INDEX IF NOT EXISTS idx_lookup_values_category ON lookup_values(category) WHERE is_active = true;
-- CREATE INDEX IF NOT EXISTS idx_lookup_values_code ON lookup_values(category, code) WHERE is_active = true;
-- CREATE INDEX IF NOT EXISTS idx_lookup_values_display_order ON lookup_values(category, display_order) WHERE is_active = true;

-- ===================================================================
-- Add constraints for data integrity
-- ===================================================================

-- Ensure ABHA number format is correct
ALTER TABLE patient_abha 
ADD CONSTRAINT IF NOT EXISTS chk_abha_number_format 
CHECK (abha_number IS NULL OR abha_number ~ '^\d{2}-\d{4}-\d{4}-\d{4}$');

-- Ensure mobile number format is correct
ALTER TABLE patient_contacts
ADD CONSTRAINT IF NOT EXISTS chk_mobile_format
CHECK (mobile_number IS NULL OR mobile_number ~ '^[+]?[0-9]{10,15}$');

-- Ensure email format is correct
ALTER TABLE patient_contacts 
ADD CONSTRAINT IF NOT EXISTS chk_email_format 
CHECK (email IS NULL OR email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Ensure age is reasonable
ALTER TABLE patients 
ADD CONSTRAINT IF NOT EXISTS chk_age_range 
CHECK (age IS NULL OR (age >= 0 AND age <= 150));

-- Ensure date of birth is not in the future
ALTER TABLE patients 
ADD CONSTRAINT IF NOT EXISTS chk_date_of_birth 
CHECK (date_of_birth IS NULL OR date_of_birth <= CURRENT_DATE);

-- Ensure policy dates are logical
ALTER TABLE patient_insurance 
ADD CONSTRAINT IF NOT EXISTS chk_policy_dates 
CHECK (policy_start_date IS NULL OR policy_end_date IS NULL OR policy_start_date <= policy_end_date);

-- ===================================================================
-- Create views for common queries
-- ===================================================================

-- View for patient summary with contact information
CREATE OR REPLACE VIEW patient_summary AS
SELECT
    p.patient_id,
    p.facility_id,
    p.first_name,
    p.middle_name,
    p.last_name,
    CONCAT_WS(' ', p.first_name, p.middle_name, p.last_name) as full_name,
    p.date_of_birth,
    p.age,
    p.gender,
    p.blood_group,
    p.registration_date,
    pc.mobile_number,
    pc.email,
    pa.abha_number,
    p.is_active,
    p.soft_deleted
FROM patients p
LEFT JOIN patient_contacts pc ON p.patient_id = pc.patient_id
LEFT JOIN patient_abha pa ON p.patient_id = pa.patient_id
WHERE p.soft_deleted = false;

-- View for patient statistics by facility
CREATE OR REPLACE VIEW patient_stats_by_facility AS
SELECT
    facility_id,
    COUNT(*) as total_patients,
    COUNT(CASE WHEN gender = 'MALE' THEN 1 END) as male_patients,
    COUNT(CASE WHEN gender = 'FEMALE' THEN 1 END) as female_patients,
    COUNT(CASE WHEN gender = 'OTHER' THEN 1 END) as other_gender_patients,
    COUNT(CASE WHEN pa.abha_number IS NOT NULL THEN 1 END) as patients_with_abha,
    AVG(age) as average_age,
    MIN(registration_date) as first_registration,
    MAX(registration_date) as last_registration
FROM patients p
LEFT JOIN patient_abha pa ON p.patient_id = pa.patient_id
WHERE p.soft_deleted = false AND p.is_active = true
GROUP BY facility_id;

-- View for recent registrations
CREATE OR REPLACE VIEW recent_patient_registrations AS
SELECT
    p.patient_id,
    p.facility_id,
    CONCAT_WS(' ', p.first_name, p.middle_name, p.last_name) as full_name,
    p.age,
    p.gender,
    p.registration_date,
    pc.mobile_number,
    pc.email
FROM patients p
LEFT JOIN patient_contacts pc ON p.patient_id = pc.patient_id
WHERE p.soft_deleted = false
  AND p.registration_date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY p.registration_date DESC;

-- ===================================================================
-- Create functions for common operations
-- ===================================================================

-- Function to get patient age from date of birth
CREATE OR REPLACE FUNCTION calculate_age(birth_date DATE)
RETURNS INTEGER AS $$
BEGIN
    IF birth_date IS NULL THEN
        RETURN NULL;
    END IF;
    
    RETURN EXTRACT(YEAR FROM AGE(CURRENT_DATE, birth_date));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to format patient name
CREATE OR REPLACE FUNCTION format_patient_name(first_name VARCHAR, middle_name VARCHAR, last_name VARCHAR)
RETURNS VARCHAR AS $$
BEGIN
    RETURN TRIM(CONCAT_WS(' ', first_name, middle_name, last_name));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to validate ABHA number format
CREATE OR REPLACE FUNCTION is_valid_abha_number(abha_number VARCHAR)
RETURNS BOOLEAN AS $$
BEGIN
    IF abha_number IS NULL THEN
        RETURN true;
    END IF;
    
    RETURN abha_number ~ '^\d{2}-\d{4}-\d{4}-\d{4}$';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- ===================================================================
-- Update table statistics for better query planning
-- ===================================================================

ANALYZE patients;
ANALYZE patient_contacts;
ANALYZE patient_addresses;
ANALYZE emergency_contacts;
ANALYZE patient_abha;
ANALYZE patient_insurance;
ANALYZE billing_referral;
ANALYZE information_sharing;
ANALYZE referrals;
ANALYZE patient_relationships;
-- ANALYZE lookup_values;

-- ===================================================================
-- Create triggers for automatic updates
-- ===================================================================

-- Trigger to automatically update age when date of birth changes
CREATE OR REPLACE FUNCTION update_patient_age()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.date_of_birth IS NOT NULL AND NEW.date_of_birth != OLD.date_of_birth THEN
        NEW.age = calculate_age(NEW.date_of_birth);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger if it doesn't exist
DROP TRIGGER IF EXISTS trigger_update_patient_age ON patients;
CREATE TRIGGER trigger_update_patient_age
    BEFORE UPDATE ON patients
    FOR EACH ROW
    EXECUTE FUNCTION update_patient_age();

-- ===================================================================
-- Add comments for documentation
-- ===================================================================

COMMENT ON TABLE patients IS 'Main patient information table with optimized indexes for performance';
COMMENT ON INDEX idx_patients_facility_id IS 'Index for filtering patients by facility';
COMMENT ON INDEX idx_patients_search_composite IS 'Composite index for common search patterns';
COMMENT ON VIEW patient_summary IS 'Optimized view for patient summary with contact information';
COMMENT ON FUNCTION calculate_age IS 'Function to calculate age from date of birth';

-- ===================================================================
-- Performance monitoring queries (for reference)
-- ===================================================================

/*
-- Query to check index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- Query to check table statistics
SELECT 
    schemaname,
    tablename,
    n_tup_ins,
    n_tup_upd,
    n_tup_del,
    n_live_tup,
    n_dead_tup,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY n_live_tup DESC;
*/
